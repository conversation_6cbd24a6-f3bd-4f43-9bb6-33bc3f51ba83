

const NodeApp = require('./NodeApp(<PERSON>).js')
const {systemLogger} = require('./AppLogger.js')

// usage
systemLogger.log('System started');
const object = { a: 1, b: 2, c: 3 };
systemLogger.warn('warning message with object', object);
systemLogger.error(new Error('Test error'));

// set a repeated log in systemLogger
setInterval(() => {
    systemLogger.log('Repeated log message modified');
}, 1000);

NodeApp.init()


module.exports = {}
