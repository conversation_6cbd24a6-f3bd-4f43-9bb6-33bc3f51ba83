const { Logger } = require('./Logger.js');
const { sseBroadcast } = require('./routes/sse');

// loggers
const startupLogger = new Logger({ 
    size: 100,
    filename: 'startup',
    filesize: 3 * 1024,
    persist: true,
    storeOnly: ['error', 'warn', 'info'],
    verboseStack: false,
    enableConsole: true,
});

const systemLogger = new Logger({
    size: 100,
    filename: 'system',
    filesize: 10 * 1024 * 1024,
    persist: true,
    storeOnly: ['error', 'warn', 'info'],
    verboseStack: false,
    enableConsole: true,
    callback: (message)=>sseBroadcast(message, 'system')
});

const alertLogger = new Logger({
    size: 100,
    filename: 'alerts',
    filesize: 10 * 1024 * 1024,
    persist: true,
    storeOnly: ['error', 'warn', 'info'],
    verboseStack: false,
    enableConsole: true,
    callback: (message)=>sseBroadcast(message, 'alerts')
});

module.exports = { startupLogger, systemLogger, alertLogger };

// test case

// startupLogger.init();
// console.log("before logging stuff", startupLogger.getLogs(limit = 10, offset = 0));
// console.log("length before", startupLogger.memoryArray.getLength());

// startupLogger.log('System started mama mia >>> logg added');

// // startupLogger.warn('warning message with object', { a: 1, b: 2, c: 3 });
// // startupLogger.error(new Error('Test error'));

// console.log("after logging stuff",startupLogger.getLogs());