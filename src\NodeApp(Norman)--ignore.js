//*********************************************************************************************
//* COPYRIGHT © 2025-, <PERSON> and Praevista Inc. - All Rights Reserved
//* NOTICE: All information contained herein is, and remains the property of <PERSON>,
//* Praevista Inc., and their suppliers, if any. The intellectual and technical concepts
//* contained herein are proprietary to <PERSON>, Praevista Inc., and their suppliers
//* and are protected by trade secret or copyright law. Dissemination of this information or
//* reproduction of this material is strictly forbidden unless prior written permission is
//* obtained from Michael <PERSON> and Praevista Inc.
//*********************************************************************************************
//
// NodeApp.js
//
// The top level file to import into NodeRed
//=============================================================================================
console.log('========== NodeApp.js ==============')

const fs = require('fs')
const Project          = require('./Project.js')
const { clearObject, startAlignedInterval, arrayToObject } = require('./Util.js')

const { init: IoServersInit   } = require('./IoServersInit.js')
const { init: IoRegistersInit } = require('./IoRegistersInit.js')
const IoService        = require('./IoService.js')
const HttpServer       = require('./HttpServer.js')
const System           = require('./System.js')
const SystemSettings   = require('./SystemSettings.js')
const Datalog          = require('./Datalog.js')
const CurvesInit       = require('./CurvesInit.js')
const ProjectConstants = require('./ProjectConstants.js')
const Schedules        = require('./Schedules.js')
const DocsInit         = require('./DocsInit.js')
const Alarms           = require('./Alarms.js')
const DataPoster       = require('./data_poster/data_poster.js')


const { sseBroadcast } = require('./routes/sse.js')

// Initialize embedded system logger
const embeddedLogger = require('./Logger.js')

const {
	ProjectDefinitions,

	IoServersArray,
	IoServersArrayDev,

	RegistersList,


	// RegistersPersist,

	// Dashboards,
	// UnitsConversions,
	Trends,
	DatalogList,
	// CurvesFile,

	Poll: NarrativePoll, // This will be called every 1 second by the system
	Init: NarrativeInit, // This will be called once at the beginning of the application
	AlarmsList,
} = Project



const Global = require('./Global.js')
const {
	init: globalInit,
	registers,
	devicesList,
	ioServers,
	datalogSettings,
	projectSettings,
	systemSettings,
	timestamp,
	trends,
} = Global

function parseCommandLineSwitches() {
	// Get all command line arguments (skip first two which are node and script path)
	return process.argv.slice(2)
		.filter(arg => arg.startsWith('-'))
		.map(switchArg => switchArg.toLowerCase());
}

//-------------------------------------------------------------------------------------------
// Command line switches
//-------------------------------------------------------------------------------------------
const cmdSwitches = parseCommandLineSwitches()
const NOIO = cmdSwitches.includes('-noio')


let SystemTimerClear = null
const SystemTimerInterval = 1000
let systemTimerCallback = null
let systemTimerCounter = 0

const 	RegistersListClean = []

// Embedded system startup state tracking
let systemStartupFatalError = false
let systemCoreServicesEnabled = false

/**
 * Parses command line arguments, extracts switches (options starting with -),
 * and converts them to lowercase
 * @returns {string[]} Array of switch strings in lowercase
 */

/**
 * Main Initialization for the Node application to be used inside the NodeRed application
 * @function init
 * @description Initializes the external NodeRed application component
 * @returns {void}
 */
function init()
{
	// Reset startup state
	systemStartupFatalError = false	//expose to http 
	systemCoreServicesEnabled = false

	embeddedLogger.startup.info('=== EMBEDDED SYSTEM STARTUP INITIATED ===');

	try {
		// Make sure the system directory exists for User login
		embeddedLogger.startup.info('Initializing system directory and user authentication');
		System.init()

		embeddedLogger.startup.info('Loading system settings from project directory');
		SystemSettings.init() // Load the system settings from the project directory

		// Load alerts from previous session
		embeddedLogger.loadAlertsFromFile();
		embeddedLogger.startup.info('Previous alert logs loaded into memory');

	} catch (error) {
		embeddedLogger.startup.fatal('CRITICAL: Failed to initialize basic system components', {
			error: error
		});
		systemStartupFatalError = true;
		// Continue with HTTP server initialization to allow log access
	}

	//-----------------------------------------------------------
	// Clear system timer
	//-----------------------------------------------------------
	SystemTimerClear?.()
	SystemTimerClear = null

	systemTimerCounter = 0
	HttpServer.close()

	//-----------------------------------------------------------
	// Load All Elements of `Global`
	//-----------------------------------------------------------
	if (!systemStartupFatalError) {
		try {
			embeddedLogger.startup.info('Initializing global components and project elements');
			globalInit()
			trends.length = 0
			trends.push(...Trends)

			embeddedLogger.startup.info('Loading project constants');
			ProjectConstants.init()

			embeddedLogger.startup.info('Initializing schedules');
			Schedules.init()

			embeddedLogger.startup.info('Initializing documentation system');
			DocsInit.init()

			embeddedLogger.startup.info('Configuring IO servers');
			const myIoServersArray = process.env.PRAEVISTA_DEBUG ? IoServersArrayDev : IoServersArray
			const tmpIoServers = arrayToObject(myIoServersArray, 'name')
			clearObject(ioServers)
			Object.assign(ioServers, tmpIoServers)

			embeddedLogger.startup.info('Initializing control curves');
			CurvesInit()

		} catch (error) {
			embeddedLogger.startup.fatal('CRITICAL: Failed to initialize global components', {
				error: error
			});
			systemStartupFatalError = true;
		}
	}

	//-----------------------------------------------------------
	// registers
	//-----------------------------------------------------------
	if (!systemStartupFatalError) {
		try {
			embeddedLogger.startup.info('Initializing IO registers');
			clearObject(registers)
			const { registersObject, registersArray } = IoRegistersInit(RegistersList, ioServers, projectSettings)
			Object.assign(registers, registersObject)
			RegistersListClean.length = 0
			RegistersListClean.push(...registersArray)
			devicesList.length = 0
			devicesList.push(...Object.keys(registers))
			embeddedLogger.startup.info(`Initialized ${registersArray.length} registers for ${devicesList.length} devices`);

			//-----------------------------------------------------------
			// ioServers - Global version used in realtime
			//-----------------------------------------------------------
			embeddedLogger.startup.info('Initializing IO servers for real-time communication');
			IoServersInit(ioServers, registersArray)
			embeddedLogger.startup.info('IO servers initialized successfully');

		} catch (error) {
			embeddedLogger.startup.fatal('CRITICAL: Failed to initialize IO registers and servers', {
				error: error
			});
			systemStartupFatalError = true;
		}
	}


	//-----------------------------------------------------------
	// HTTP Server - Always initialize to allow log access
	//-----------------------------------------------------------
	try {
		embeddedLogger.startup.info('Starting HTTP server for web interface and API access');
		HttpServer.init()
		embeddedLogger.startup.info('HTTP server started successfully');
	} catch (error) {
		embeddedLogger.startup.error('Failed to start HTTP server', {
			error: error
		});
		// HTTP server failure is not fatal - system can still run
	}

	//-----------------------------------------------------------
	// Datalog
	//-----------------------------------------------------------
	if (!systemStartupFatalError) {
		try {
			embeddedLogger.startup.info('Initializing data logging system');
			clearObject(datalogSettings)
			Object.assign(datalogSettings, Datalog.init(ProjectDefinitions, DatalogList, registers))

			embeddedLogger.startup.info('Restoring persistent registers from previous session');
			Datalog.restorePersist(datalogSettings.paths.persist, registers)

			embeddedLogger.startup.info('Updating datalog metadata files');
			fs.writeFileSync(datalogSettings.paths.metadata, JSON.stringify(datalogSettings.metadata, null, '\t'))
			fs.writeFileSync(datalogSettings.paths.metacsv, Datalog.metadataToCsv(datalogSettings.metadata))
			embeddedLogger.startup.info('Data logging system initialized successfully');

		} catch (error) {
			embeddedLogger.startup.fatal('CRITICAL: Failed to initialize data logging system', {
				error: error
			});
			systemStartupFatalError = true;
		}
	}

	//-----------------------------------------------------------
	// Data Poster - Initialize with required configuration
	//-----------------------------------------------------------
	if (!systemStartupFatalError) {
		try {
			embeddedLogger.startup.info('Initializing cloud data poster');
			const dataPosterConfig = {
				fine: datalogSettings.paths.fine,
				metadata: datalogSettings.paths.metadata,
				serverUrl: systemSettings.cloudUrl,
				enable: systemSettings.cloudEnable,
			}
			DataPoster.init(dataPosterConfig)
			embeddedLogger.startup.info('Cloud data poster initialized successfully');
		} catch (error) {
			embeddedLogger.startup.error('Failed to initialize cloud data poster', {
				error: error
			});
			// Data poster failure is not fatal
		}
	}

	//-----------------------------------------------------------
	// Init the Alarms List from the Project Directory
	//-----------------------------------------------------------
	if (!systemStartupFatalError) {
		try {
			embeddedLogger.startup.info('Initializing alarms system');
			Alarms.init(AlarmsList, ProjectDefinitions.alarmSettingsFile)
			embeddedLogger.startup.info('Alarms system initialized successfully');
		} catch (error) {
			embeddedLogger.startup.fatal('CRITICAL: Failed to initialize alarms system', {
				error: error
			});
			systemStartupFatalError = true;
		}
	}

	//-----------------------------------------------------------
	// Narrative from the Project Directory
	//-----------------------------------------------------------
	if (!systemStartupFatalError) {
		try {
			embeddedLogger.startup.info('Loading project narrative (core logic)');
			NarrativeInit(Global)
			embeddedLogger.startup.info('Project narrative loaded successfully');
		} catch (error) {
			embeddedLogger.startup.fatal('CRITICAL: Failed to load project narrative', {
				error: error
			});
			systemStartupFatalError = true;
		}
	}

	//-----------------------------------------------------------
	// System Timer and Core Services
	//-----------------------------------------------------------
	if (systemStartupFatalError) {
		embeddedLogger.startup.fatal('=== STARTUP COMPLETED WITH FATAL ERRORS ===', {
			message: 'Core system functions disabled due to fatal startup errors. HTTP services remain active for log access.'
		});
		systemCoreServicesEnabled = false;
	} else {
		embeddedLogger.startup.info('=== STARTUP COMPLETED SUCCESSFULLY ===');
		systemCoreServicesEnabled = true;

		embeddedLogger.startup.info('Starting main polling loop');
		SystemTimerClear = startAlignedInterval(() => {
			const ms = Date.now()
			timestamp.ms = ms
			timestamp.s = Math.floor(ms / 1000)

			// Only run core services if startup was successful
			if (systemCoreServicesEnabled) {
				try {
					//-------------------------------------------------------
					// START of Main Polling Loop
					//-------------------------------------------------------
					Schedules.svc() // Poll the Project Schedules

					NarrativePoll(Global)  // Poll the Project Narrative

					Alarms.svc() // Poll the Project Alarms

					if(!NOIO) {
						IoService.poll(ioServers, registers, timestamp.s) // Poll the Modbus devices
					}
				} catch (error) {
					embeddedLogger.operational.error('Error in main polling loop', {
						error: error
					});
				}
			}

			// The IoService.poll() should be finished by now
			if (systemCoreServicesEnabled) {
				setTimeout(function() {
					try {
						const datalog = Datalog.svc(timestamp, datalogSettings) // Log the data

						if(datalog) // Something just got written to the datalog
						{
							// Call the cloud data pusher
							if (systemSettings.cloudEnable && systemSettings.cloudUrl){
								DataPoster.svc({
									enable: systemSettings.cloudEnable,
									serverUrl: systemSettings.cloudUrl
								}, datalog.data)
							}
						}
					} catch (error) {
						embeddedLogger.operational.error('Error in datalog service', {
							error: error
						});
					}

					sseBroadcast({         // Broadcast Server Sent Events
						t: timestamp.s,
						count: systemTimerCounter,
						coreServicesEnabled: systemCoreServicesEnabled,
						startupFatalError: systemStartupFatalError
					})
				}, 500)
			} else {
				// Even with fatal errors, broadcast basic status
				sseBroadcast({
					t: timestamp.s,
					count: systemTimerCounter,
					coreServicesEnabled: systemCoreServicesEnabled,
					startupFatalError: systemStartupFatalError,
					message: 'Core services disabled due to startup errors'
				})
			}

			//-------------------------------------------------------
			// END of Main Polling Loop
			//-------------------------------------------------------

			// If a callback has been registred, call it
			systemTimerCallback?.({timestamp, count: systemTimerCounter})

			systemTimerCounter++
	}, SystemTimerInterval);
}
}


/**
 * Sets a callback function to be executed on each system timer tick
 * @function setSystemTimerCallback
 * @param {Function} callback - The callback function to be called on each timer tick
 * @param {Object} callback.params - The parameters passed to the callback
 * @param {Object} callback.params.timestamp - The current timestamp object
 * @param {number} callback.params.count - The current timer counter value
 * @returns {void}
 */
function setSystemTimerCallback(callback) {
	systemTimerCallback = callback
}



// process.on('SIGINT', function() {
// 	socket.close()
// 	process.exit()
// })

async function gracefulShutdown()
{
	console.log('Received kill signal, shutting down gracefully')

	systemTimerCallback = null
	SystemTimerClear?.()
	SystemTimerClear = null

	console.log('System Timer closed')


	try {
		await IoService.close(ioServers)
		console.log('IoService closed')
	} catch (err) {
		console.error('gracefulShutdown IoService.close Error', err)
	}

	// Save persisted registers
	Datalog.savePersist(datalogSettings)

	try {
		await DataPoster.shutdown()
		console.log('DataPoster shutdown complete')
	} catch (err) {
		console.error('gracefulShutdown DataPoster.shutdown Error', err)
	}

	try {
		await HttpServer.close()
		console.log('HttpServer closed')
	} catch (err) {
		console.error('gracefulShutdown HttpServer.close Error', err)
	}


	process.exit(0)


	// return HttpServer.close().then(() => {
	// 	console.log('HttpServer closed')
	// 	return IoService.close(ioServers)
	// }).then(() => {
	// 	console.log('IoService closed')
	// 	process.exit(0)
	// }).catch(err => {
	// 	console.error('gracefulShutdown Error', err)
	// 	process.exit(1)
	// })




}

process.on('SIGTERM', gracefulShutdown)
process.on('SIGINT', gracefulShutdown)



module.exports = {
	init,
	// ioPoll,
	ioServers,
	registers,
	RegistersList,
	RegistersListClean,
	projectSettings,     // Defined in /public/project-settings.json
	timestamp,
	env: process.env,
	// systemTimerCallback,
	setSystemTimerCallback, // setSystemTimerCallback(ioServers)
}

